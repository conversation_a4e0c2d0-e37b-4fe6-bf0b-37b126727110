import React, { useRef, useEffect, useState, useCallback } from 'react';

export interface Point {
  x: number;
  y: number;
  pressure: number;
  timestamp: number;
}

export interface Stroke {
  points: Point[];
  color: string;
  baseWidth: number;
  opacity: number;
  id: string;
}

export interface DrawingSettings {
  brushSize: number;
  color: string;
  opacity: number;
  pressureSensitivity: boolean;
  jitterAmount: number;
  shadowEnabled: boolean;
  shadowBlur: number;
  shadowOffset: { x: number; y: number };
  shadowOpacity: number;
}

interface DrawingCanvasProps {
  width: number;
  height: number;
  settings: DrawingSettings;
  onStrokeComplete?: (stroke: Stroke) => void;
  strokes?: Stroke[];
  className?: string;
  readOnly?: boolean; // For preview mode
}

export const DrawingCanvas: React.FC<DrawingCanvasProps> = ({
  width,
  height,
  settings,
  onStrokeComplete,
  strokes = [],
  className,
  readOnly = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentStroke, setCurrentStroke] = useState<Point[]>([]);

  // Get pointer pressure if available
  const getPointerPressure = useCallback((event: PointerEvent): number => {
    if (event.pressure !== undefined && event.pressure > 0) {
      return event.pressure;
    }
    // Fallback for devices without pressure sensitivity
    return 0.5;
  }, []);

  // Add jitter to coordinates for natural look
  const addJitter = useCallback((x: number, y: number, amount: number): { x: number; y: number } => {
    if (amount === 0) return { x, y };
    
    const jitterX = (Math.random() - 0.5) * amount;
    const jitterY = (Math.random() - 0.5) * amount;
    
    return {
      x: x + jitterX,
      y: y + jitterY
    };
  }, []);

  // Calculate stroke width based on pressure and settings
  const calculateStrokeWidth = useCallback((pressure: number, baseWidth: number): number => {
    if (!settings.pressureSensitivity) {
      return baseWidth;
    }
    
    // Pressure-sensitive width calculation
    const minWidth = baseWidth * 0.3;
    const maxWidth = baseWidth * 1.5;
    return minWidth + (maxWidth - minWidth) * pressure;
  }, [settings.pressureSensitivity]);

  // Draw a single stroke with all effects
  const drawStroke = useCallback((ctx: CanvasRenderingContext2D, stroke: Stroke) => {
    if (stroke.points.length < 2) return;

    ctx.save();
    
    // Set up shadow if enabled
    if (settings.shadowEnabled) {
      ctx.shadowColor = `rgba(0, 0, 0, ${settings.shadowOpacity})`;
      ctx.shadowBlur = settings.shadowBlur;
      ctx.shadowOffsetX = settings.shadowOffset.x;
      ctx.shadowOffsetY = settings.shadowOffset.y;
    }

    ctx.strokeStyle = stroke.color;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.globalAlpha = stroke.opacity;

    // Draw stroke with varying width
    for (let i = 0; i < stroke.points.length - 1; i++) {
      const currentPoint = stroke.points[i];
      const nextPoint = stroke.points[i + 1];
      
      const currentWidth = calculateStrokeWidth(currentPoint.pressure, stroke.baseWidth);
      const nextWidth = calculateStrokeWidth(nextPoint.pressure, stroke.baseWidth);
      
      // Apply jitter
      const jitteredCurrent = addJitter(currentPoint.x, currentPoint.y, settings.jitterAmount);
      const jitteredNext = addJitter(nextPoint.x, nextPoint.y, settings.jitterAmount);
      
      // Create gradient for smooth width transition
      const gradient = ctx.createLinearGradient(
        jitteredCurrent.x, jitteredCurrent.y,
        jitteredNext.x, jitteredNext.y
      );
      gradient.addColorStop(0, stroke.color);
      gradient.addColorStop(1, stroke.color);
      
      ctx.beginPath();
      ctx.lineWidth = (currentWidth + nextWidth) / 2;
      ctx.moveTo(jitteredCurrent.x, jitteredCurrent.y);
      ctx.lineTo(jitteredNext.x, jitteredNext.y);
      ctx.stroke();
    }
    
    ctx.restore();
  }, [settings, calculateStrokeWidth, addJitter]);

  // Redraw all strokes
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw all completed strokes
    strokes.forEach(stroke => drawStroke(ctx, stroke));
    
    // Draw current stroke if drawing
    if (currentStroke.length > 1) {
      const tempStroke: Stroke = {
        points: currentStroke,
        color: settings.color,
        baseWidth: settings.brushSize,
        opacity: settings.opacity,
        id: 'temp'
      };
      drawStroke(ctx, tempStroke);
    }
  }, [strokes, currentStroke, settings, width, height, drawStroke]);

  // Handle pointer events
  const handlePointerDown = useCallback((event: React.PointerEvent) => {
    if (readOnly) return;
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const pressure = getPointerPressure(event.nativeEvent as PointerEvent);
    
    setIsDrawing(true);
    setCurrentStroke([{
      x,
      y,
      pressure,
      timestamp: Date.now()
    }]);
    
    // Capture pointer for better tracking
    canvas.setPointerCapture(event.pointerId);
  }, [getPointerPressure]);

  const handlePointerMove = useCallback((event: React.PointerEvent) => {
    if (readOnly || !isDrawing) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const pressure = getPointerPressure(event.nativeEvent as PointerEvent);
    
    setCurrentStroke(prev => [...prev, {
      x,
      y,
      pressure,
      timestamp: Date.now()
    }]);
  }, [isDrawing, getPointerPressure]);

  const handlePointerUp = useCallback((event: React.PointerEvent) => {
    if (readOnly || !isDrawing) return;
    
    setIsDrawing(false);
    
    if (currentStroke.length > 1) {
      const stroke: Stroke = {
        points: currentStroke,
        color: settings.color,
        baseWidth: settings.brushSize,
        opacity: settings.opacity,
        id: `stroke-${Date.now()}-${Math.random()}`
      };
      
      onStrokeComplete?.(stroke);
    }
    
    setCurrentStroke([]);
    
    // Release pointer capture
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.releasePointerCapture(event.pointerId);
    }
  }, [isDrawing, currentStroke, settings, onStrokeComplete]);

  // Redraw when strokes or settings change
  useEffect(() => {
    redrawCanvas();
  }, [redrawCanvas]);

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className={className}
      onPointerDown={readOnly ? undefined : handlePointerDown}
      onPointerMove={readOnly ? undefined : handlePointerMove}
      onPointerUp={readOnly ? undefined : handlePointerUp}
      onPointerLeave={readOnly ? undefined : handlePointerUp}
      style={{
        touchAction: readOnly ? 'auto' : 'none', // Prevent scrolling while drawing
        cursor: readOnly ? 'default' : 'crosshair'
      }}
    />
  );
};
