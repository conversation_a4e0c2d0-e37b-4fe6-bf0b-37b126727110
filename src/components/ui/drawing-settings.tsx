import React from 'react';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import {
  Brush,
  Palette,
  Zap,
  Waves,
  Shadow,
  <PERSON>otateCcw,
  Eraser
} from 'lucide-react';
import { DrawingSettings } from './drawing-canvas';

// Re-export for convenience
export type { DrawingSettings } from './drawing-canvas';

interface DrawingSettingsProps {
  settings: DrawingSettings;
  onSettingsChange: (settings: DrawingSettings) => void;
  onClearCanvas?: () => void;
  onUndo?: () => void;
  className?: string;
}

const colorPresets = [
  { name: 'Black', value: '#000000' },
  { name: 'Blue', value: '#1e40af' },
  { name: 'Red', value: '#dc2626' },
  { name: '<PERSON>', value: '#16a34a' },
  { name: '<PERSON>', value: '#9333ea' },
  { name: '<PERSON>', value: '#a16207' },
  { name: '<PERSON>', value: '#6b7280' },
  { name: 'Navy', value: '#1e3a8a' }
];

export const DrawingSettingsPanel: React.FC<DrawingSettingsProps> = ({
  settings,
  onSettingsChange,
  onClearCanvas,
  onUndo,
  className
}) => {
  const updateSetting = <K extends keyof DrawingSettings>(
    key: K,
    value: DrawingSettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const updateShadowOffset = (axis: 'x' | 'y', value: number) => {
    onSettingsChange({
      ...settings,
      shadowOffset: {
        ...settings.shadowOffset,
        [axis]: value
      }
    });
  };

  return (
    <Card className={`p-6 space-y-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Brush className="h-5 w-5" />
          Drawing Tools
        </h3>
        <div className="flex gap-2">
          {onUndo && (
            <Button
              onClick={onUndo}
              variant="outline"
              size="sm"
              className="bg-white/50 dark:bg-black/50"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}
          {onClearCanvas && (
            <Button
              onClick={onClearCanvas}
              variant="outline"
              size="sm"
              className="bg-white/50 dark:bg-black/50"
            >
              <Eraser className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Brush Settings */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Brush className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
          <Label className="font-medium">Brush Settings</Label>
        </div>
        
        <div className="space-y-3">
          <div>
            <Label className="text-sm text-neutral-600 dark:text-neutral-400">
              Brush Size: {settings.brushSize}px
            </Label>
            <Slider
              value={[settings.brushSize]}
              onValueChange={([value]) => updateSetting('brushSize', value)}
              min={1}
              max={50}
              step={1}
              className="mt-2"
            />
          </div>
          
          <div>
            <Label className="text-sm text-neutral-600 dark:text-neutral-400">
              Opacity: {Math.round(settings.opacity * 100)}%
            </Label>
            <Slider
              value={[settings.opacity]}
              onValueChange={([value]) => updateSetting('opacity', value)}
              min={0.1}
              max={1}
              step={0.1}
              className="mt-2"
            />
          </div>
        </div>
      </div>

      {/* Color Selection */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
          <Label className="font-medium">Color</Label>
        </div>
        
        <div className="grid grid-cols-4 gap-2">
          {colorPresets.map((color) => (
            <button
              key={color.value}
              onClick={() => updateSetting('color', color.value)}
              className={`w-10 h-10 rounded-lg border-2 transition-all ${
                settings.color === color.value
                  ? 'border-indigo-500 scale-110'
                  : 'border-neutral-300 dark:border-neutral-600 hover:scale-105'
              }`}
              style={{ backgroundColor: color.value }}
              title={color.name}
            />
          ))}
        </div>
        
        <div>
          <Label className="text-sm text-neutral-600 dark:text-neutral-400">Custom Color</Label>
          <input
            type="color"
            value={settings.color}
            onChange={(e) => updateSetting('color', e.target.value)}
            className="mt-2 w-full h-10 rounded-lg border border-neutral-300 dark:border-neutral-600"
          />
        </div>
      </div>

      {/* Pressure Sensitivity */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Zap className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
          <Label className="font-medium">Pressure Sensitivity</Label>
        </div>
        
        <div className="flex items-center justify-between">
          <Label className="text-sm text-neutral-600 dark:text-neutral-400">
            Enable pressure sensitivity
          </Label>
          <Switch
            checked={settings.pressureSensitivity}
            onCheckedChange={(checked) => updateSetting('pressureSensitivity', checked)}
          />
        </div>
        
        <p className="text-xs text-neutral-500 dark:text-neutral-400">
          Works with stylus and touch devices that support pressure
        </p>
      </div>

      {/* Line Irregularities */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Waves className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
          <Label className="font-medium">Natural Look</Label>
        </div>
        
        <div>
          <Label className="text-sm text-neutral-600 dark:text-neutral-400">
            Jitter Amount: {settings.jitterAmount.toFixed(1)}px
          </Label>
          <Slider
            value={[settings.jitterAmount]}
            onValueChange={([value]) => updateSetting('jitterAmount', value)}
            min={0}
            max={3}
            step={0.1}
            className="mt-2"
          />
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
            Adds slight randomness to make strokes look more natural
          </p>
        </div>
      </div>

      {/* Shadow and Depth */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Shadow className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
          <Label className="font-medium">Shadow & Depth</Label>
        </div>
        
        <div className="flex items-center justify-between">
          <Label className="text-sm text-neutral-600 dark:text-neutral-400">
            Enable shadow
          </Label>
          <Switch
            checked={settings.shadowEnabled}
            onCheckedChange={(checked) => updateSetting('shadowEnabled', checked)}
          />
        </div>
        
        {settings.shadowEnabled && (
          <div className="space-y-3 pl-4 border-l-2 border-neutral-200 dark:border-neutral-700">
            <div>
              <Label className="text-sm text-neutral-600 dark:text-neutral-400">
                Shadow Blur: {settings.shadowBlur}px
              </Label>
              <Slider
                value={[settings.shadowBlur]}
                onValueChange={([value]) => updateSetting('shadowBlur', value)}
                min={0}
                max={10}
                step={0.5}
                className="mt-2"
              />
            </div>
            
            <div>
              <Label className="text-sm text-neutral-600 dark:text-neutral-400">
                Shadow Opacity: {Math.round(settings.shadowOpacity * 100)}%
              </Label>
              <Slider
                value={[settings.shadowOpacity]}
                onValueChange={([value]) => updateSetting('shadowOpacity', value)}
                min={0.1}
                max={0.8}
                step={0.1}
                className="mt-2"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm text-neutral-600 dark:text-neutral-400">
                  Offset X: {settings.shadowOffset.x}px
                </Label>
                <Slider
                  value={[settings.shadowOffset.x]}
                  onValueChange={([value]) => updateShadowOffset('x', value)}
                  min={-5}
                  max={5}
                  step={0.5}
                  className="mt-2"
                />
              </div>
              
              <div>
                <Label className="text-sm text-neutral-600 dark:text-neutral-400">
                  Offset Y: {settings.shadowOffset.y}px
                </Label>
                <Slider
                  value={[settings.shadowOffset.y]}
                  onValueChange={([value]) => updateShadowOffset('y', value)}
                  min={-5}
                  max={5}
                  step={0.5}
                  className="mt-2"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default DrawingSettingsPanel;
