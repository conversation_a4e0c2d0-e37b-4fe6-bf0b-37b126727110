import React, { useState, useCallback } from 'react';
import { DrawingCanvas, DrawingSettings, Stroke } from '@/components/ui/drawing-canvas';
import { DrawingSettingsPanel } from '@/components/ui/drawing-settings';
import { Card } from '@/components/ui/card';

const DrawingTest = () => {
  const [strokes, setStrokes] = useState<Stroke[]>([]);
  const [drawingSettings, setDrawingSettings] = useState<DrawingSettings>({
    brushSize: 3,
    color: '#000000',
    opacity: 1,
    pressureSensitivity: true,
    jitterAmount: 0.5,
    shadowEnabled: true,
    shadowBlur: 2,
    shadowOffset: { x: 1, y: 1 },
    shadowOpacity: 0.3
  });

  const handleStrokeComplete = useCallback((stroke: Stroke) => {
    setStrokes(prev => [...prev, stroke]);
  }, []);

  const handleClearCanvas = useCallback(() => {
    setStrokes([]);
  }, []);

  const handleUndoStroke = useCallback(() => {
    setStrokes(prev => prev.slice(0, -1));
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Drawing Components Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Drawing Canvas */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Drawing Canvas</h2>
            <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
              <DrawingCanvas
                width={600}
                height={400}
                settings={drawingSettings}
                onStrokeComplete={handleStrokeComplete}
                strokes={strokes}
                className="w-full h-full"
              />
            </div>
            <div className="mt-4 text-sm text-gray-600">
              <div>Strokes: {strokes.length}</div>
              <div>Brush Size: {drawingSettings.brushSize}px</div>
              <div>Color: {drawingSettings.color}</div>
              <div>Pressure Sensitivity: {drawingSettings.pressureSensitivity ? 'On' : 'Off'}</div>
            </div>
          </Card>

          {/* Drawing Settings */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Drawing Settings</h2>
            <DrawingSettingsPanel
              settings={drawingSettings}
              onSettingsChange={setDrawingSettings}
              onClearCanvas={handleClearCanvas}
              onUndo={handleUndoStroke}
              className="bg-transparent border-none shadow-none p-0"
            />
          </Card>
        </div>

        {/* Preview Area */}
        <Card className="p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">Read-Only Preview</h2>
          <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
            <DrawingCanvas
              width={600}
              height={300}
              settings={drawingSettings}
              strokes={strokes}
              className="w-full h-full"
              readOnly={true}
            />
          </div>
          <p className="mt-2 text-sm text-gray-600">
            This canvas shows the same strokes but is read-only (no interaction)
          </p>
        </Card>

        {/* Instructions */}
        <Card className="p-6 mt-8 bg-blue-50">
          <h3 className="text-lg font-semibold mb-2">Instructions</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Draw on the canvas using your mouse, touch, or stylus</li>
            <li>Adjust brush settings in the right panel</li>
            <li>Try pressure sensitivity with a compatible stylus</li>
            <li>Use the jitter setting to make strokes look more natural</li>
            <li>Enable shadows for depth effects</li>
            <li>Use the undo and clear buttons to manage your drawing</li>
          </ul>
        </Card>
      </div>
    </div>
  );
};

export default DrawingTest;
